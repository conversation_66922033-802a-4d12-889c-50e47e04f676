<?php
/**
 * Create Order API Endpoint
 *
 * This endpoint allows users to create a new order with payment information
 */

// Include required files
require_once '../../config/config.php';
require_once '../../config/db.php';
require_once '../../includes/functions.php';
require_once '../../includes/OrderManager.php';
require_once '../../includes/UserManager.php';
require_once '../../includes/PromoCodeManager.php';
require_once '../../includes/SettingsManager.php';
require_once '../../includes/FCMService.php';
require_once '../../includes/NotificationManager.php';

// Initialize managers
$orderManager = new OrderManager($pdo);
$userManager = new UserManager($pdo);
$promoCodeManager = new PromoCodeManager($pdo);
$settingsManager = new SettingsManager($pdo);

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get request data
$userId = isset($_POST['user_id']) ? (int)$_POST['user_id'] : null;
$items = isset($_POST['items']) ? $_POST['items'] : null;
$subtotal = isset($_POST['subtotal']) ? (float)$_POST['subtotal'] : 0;
$discount = isset($_POST['discount']) ? (float)$_POST['discount'] : 0;
// Get delivery fee from settings or use the one provided in the request
$defaultDeliveryFee = (float)$settingsManager->getSetting('delivery_fee', 50.00);
$deliveryFee = isset($_POST['delivery_fee']) ? (float)$_POST['delivery_fee'] : $defaultDeliveryFee;
$total = isset($_POST['total']) ? (float)$_POST['total'] : 0;
$paymentMethod = isset($_POST['payment_method']) ? $_POST['payment_method'] : 'cash';
$pickupAddress = isset($_POST['pickup_address']) ? $_POST['pickup_address'] : null;
$pickupDate = isset($_POST['pickup_date']) ? $_POST['pickup_date'] : null;
$pickupTimeSlot = isset($_POST['pickup_time_slot']) ? $_POST['pickup_time_slot'] : null;
$deliveryAddress = isset($_POST['delivery_address']) ? $_POST['delivery_address'] : null;
$notes = isset($_POST['notes']) ? $_POST['notes'] : null;

// Payment information
$transactionId = isset($_POST['transaction_id']) ? $_POST['transaction_id'] : null;
$paymentProvider = isset($_POST['payment_provider']) ? $_POST['payment_provider'] : null;
$cardNumber = isset($_POST['card_number']) ? $_POST['card_number'] : null;

// Validate required parameters
if (!$userId) {
    jsonResponse(false, 'Missing required parameter: user_id', [], 400);
}

if (!$items) {
    jsonResponse(false, 'Missing required parameter: items', [], 400);
}

if (!$pickupAddress) {
    jsonResponse(false, 'Missing required parameter: pickup_address', [], 400);
}

if (!$pickupDate) {
    jsonResponse(false, 'Missing required parameter: pickup_date', [], 400);
}

if (!$pickupTimeSlot) {
    jsonResponse(false, 'Missing required parameter: pickup_time_slot', [], 400);
}

if (!$deliveryAddress) {
    jsonResponse(false, 'Missing required parameter: delivery_address', [], 400);
}

// Log payment information for debugging
error_log("Payment Method: " . $paymentMethod);
error_log("Transaction ID: " . $transactionId);
error_log("Payment Provider: " . $paymentProvider);

// Normalize and validate payment information based on payment method
$originalPaymentMethod = $paymentMethod;

// Normalize payment method names
if ($paymentMethod === 'Cash on Delivery' || strtolower($paymentMethod) === 'cash') {
    // Cash on Delivery doesn't require transaction ID
    $paymentMethod = 'Cash on Delivery';
    $paymentProvider = 'Cash';
    // For Cash on Delivery, transaction ID can be empty
    $transactionId = '';

    error_log("Validated Cash on Delivery payment");
} else if ($paymentMethod === 'Credit/Debit Card' || strtolower($paymentMethod) === 'card') {
    if (empty($cardNumber) && empty($transactionId)) {
        error_log("Card payment validation failed: No card number or transaction ID provided");
        jsonResponse(false, 'Card number is required for card payments', [], 400);
    }
    $paymentMethod = 'Credit/Debit Card';
    $paymentProvider = 'Card';

    // If transaction ID is empty but card number is provided, use card number as transaction ID
    if (empty($transactionId) && !empty($cardNumber)) {
        $transactionId = $cardNumber;
    }

    error_log("Validated Credit/Debit Card payment with transaction ID: " . $transactionId);
} else if (in_array($paymentMethod, ['bKash', 'Nagad', 'Rocket'])) {
    if (empty($transactionId)) {
        error_log("Mobile banking validation failed: No transaction ID provided for " . $paymentMethod);
        jsonResponse(false, 'Transaction ID is required for mobile banking payments', [], 400);
    }
    $paymentProvider = $paymentMethod;

    error_log("Validated mobile banking payment (" . $paymentMethod . ") with transaction ID: " . $transactionId);
} else {
    error_log("Invalid payment method received: " . $originalPaymentMethod . " (normalized: " . $paymentMethod . ")");
    jsonResponse(false, 'Invalid payment method: ' . $originalPaymentMethod, [], 400);
}

// Decode items JSON
$orderItems = json_decode($items, true);
if (!$orderItems || !is_array($orderItems)) {
    jsonResponse(false, 'Invalid items format', [], 400);
}

// Verify user exists
$user = $userManager->getUserById($userId);
if (!$user) {
    jsonResponse(false, 'User not found', [], 404);
}

try {
    // Start transaction
    $pdo->beginTransaction();

    // Generate unique order number and tracking number
    $orderNumber = 'ORD' . date('ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    $trackingNumber = 'TRK' . date('ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

    // Determine payment status based on payment method
    // All payments should start as 'pending' and be updated by admin approval
    $paymentStatus = 'pending';

    if ($paymentMethod === 'Cash on Delivery') {
        $paymentStatus = 'pending'; // Will be paid on delivery
        error_log("Payment status set to 'pending' for Cash on Delivery - will be paid on delivery");
    } elseif ($paymentMethod === 'Credit/Debit Card') {
        // Card payments should be verified by admin before marking as paid
        $paymentStatus = 'pending'; // Requires admin verification
        error_log("Payment status set to 'pending' for Credit/Debit Card - requires admin verification");
    } elseif (in_array($paymentMethod, ['bKash', 'Nagad', 'Rocket']) && !empty($transactionId)) {
        // Mobile banking payments require admin verification
        $paymentStatus = 'pending'; // Requires admin verification
        error_log("Payment status set to 'pending' for " . $paymentMethod . " - requires admin verification of transaction ID: " . $transactionId);
    } else {
        $paymentStatus = 'pending'; // Default to pending for any other case
        error_log("Payment status defaulted to 'pending' for payment method: " . $paymentMethod);
    }

    // Insert order
    $stmt = $pdo->prepare("
        INSERT INTO orders (
            order_number, tracking_number, user_id,
            subtotal, discount, delivery_fee, total,
            payment_method, payment_status, status,
            pickup_address, pickup_date, pickup_time_slot,
            delivery_address, notes, transaction_id, payment_provider
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
        )
    ");

    $stmt->execute([
        $orderNumber,
        $trackingNumber,
        $userId,
        $subtotal,
        $discount,
        $deliveryFee,
        $total,
        $paymentMethod,
        $paymentStatus,
        'placed', // Initial status
        $pickupAddress,
        $pickupDate,
        $pickupTimeSlot,
        $deliveryAddress,
        $notes,
        $transactionId,
        $paymentProvider
    ]);

    // Get order ID
    $orderId = $pdo->lastInsertId();

    // Insert order items
    $stmt = $pdo->prepare("
        INSERT INTO order_items (
            order_id, item_id, quantity, price, subtotal
        ) VALUES (?, ?, ?, ?, ?)
    ");

    foreach ($orderItems as $item) {
        // Check if we're using item_id or id field
        $itemId = isset($item['item_id']) ? (int)$item['item_id'] : (isset($item['id']) ? (int)$item['id'] : 0);
        $quantity = isset($item['quantity']) ? (float)$item['quantity'] : 1;
        $price = isset($item['price']) ? (float)$item['price'] : 0;
        $itemSubtotal = isset($item['total']) ? (float)$item['total'] : (isset($item['subtotal']) ? (float)$item['subtotal'] : ($price * $quantity));

        // Log item details for debugging
        error_log("Processing order item: ID={$itemId}, Quantity={$quantity}, Price={$price}, Subtotal={$itemSubtotal}");

        // Verify item exists in database
        $checkStmt = $pdo->prepare("SELECT id, name, price FROM items WHERE id = ?");
        $checkStmt->execute([$itemId]);
        $itemExists = $checkStmt->fetch();

        if ($itemExists) {
            error_log("Item found in database: ID={$itemExists['id']}, Name={$itemExists['name']}, Price={$itemExists['price']}");

            try {
                $stmt->execute([
                    $orderId,
                    $itemId,
                    $quantity,
                    $price,
                    $itemSubtotal
                ]);

                error_log("Successfully added item ID {$itemId} to order {$orderId}");
            } catch (PDOException $e) {
                error_log("Error adding item ID {$itemId} to order: " . $e->getMessage());
                throw $e; // Re-throw to be caught by the main try-catch block
            }
        } else {
            error_log("Item ID {$itemId} not found in database");
            throw new PDOException("Item ID {$itemId} not found in database");
        }
    }

    // Create order status history entry
    $stmt = $pdo->prepare("
        INSERT INTO order_status_history (
            order_id, status, notes, updated_by_type
        ) VALUES (?, ?, ?, ?)
    ");

    $stmt->execute([
        $orderId,
        'placed',
        'Order created via mobile app',
        'user'
    ]);

    // Create notification for the user
    $stmt = $pdo->prepare("
        INSERT INTO notifications (
            user_id, order_id, title, message, type, fcm_sent, sms_sent
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
    ");

    $stmt->execute([
        $userId,
        $orderId,
        'Order Placed',
        "Your order #$orderNumber has been placed successfully. Track your order with tracking number: $trackingNumber",
        'order_status',
        0, // fcm_sent
        0  // sms_sent
    ]);

    // Commit transaction
    $pdo->commit();

    // Log successful order creation
    error_log("Order created successfully with ID: " . $orderId);
    error_log("Order Number: " . $orderNumber);
    error_log("Tracking Number: " . $trackingNumber);
    error_log("Payment Method: " . $paymentMethod);
    error_log("Payment Status: " . $paymentStatus);
    error_log("Transaction ID: " . $transactionId);
    error_log("Payment Provider: " . $paymentProvider);

    // Send FCM notification to admin users about new order
    try {
        $fcmService = new FCMService();
        $notificationManager = new NotificationManager($pdo);

        // Prepare notification data
        $notificationTitle = "New Order Received";
        $notificationMessage = "Order #$orderNumber has been placed by {$user['full_name']}. Total: ৳" . number_format($total, 2);
        $notificationData = [
            'type' => 'new_order',
            'order_id' => $orderId,
            'order_number' => $orderNumber,
            'customer_name' => $user['full_name'],
            'total' => $total,
            'timestamp' => time()
        ];

        // Get all admin users with active FCM tokens
        $stmt = $pdo->prepare("
            SELECT DISTINCT ft.token, ft.device_type, au.full_name
            FROM fcm_tokens ft
            JOIN admin_users au ON ft.user_id = au.id
            WHERE ft.is_active = 1 AND ft.device_type = 'web'
        ");
        $stmt->execute();
        $adminTokens = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (!empty($adminTokens)) {
            // Send FCM notification to all admin users
            $fcmResult = $fcmService->sendToMultipleTokens($adminTokens, $notificationTitle, $notificationMessage, $notificationData);

            // Log FCM notification result
            error_log("FCM notification sent to " . count($adminTokens) . " admin users for new order #$orderNumber");
        } else {
            error_log("No admin FCM tokens found for new order notification");
        }

    } catch (Exception $e) {
        error_log("Error sending FCM notification for new order: " . $e->getMessage());
        // Don't fail the order creation if FCM fails
    }

    // Get the created order with all details
    $order = $orderManager->getOrderById($orderId);

    // Return success response
    jsonResponse(true, 'Order created successfully', $order);

} catch (PDOException $e) {
    // Rollback transaction
    $pdo->rollBack();

    // Log detailed error information
    error_log('Database Error in create.php: ' . $e->getMessage());
    error_log('Error Code: ' . $e->getCode());

    if (isset($e->errorInfo)) {
        error_log('SQL State: ' . $e->errorInfo[0]);
        error_log('SQL Error: ' . $e->errorInfo[2]);
    }

    error_log('Stack Trace: ' . $e->getTraceAsString());

    // Log request parameters for debugging
    error_log('Request Parameters:');
    error_log('User ID: ' . $userId);
    error_log('Payment Method: ' . $paymentMethod);
    error_log('Transaction ID: ' . $transactionId);
    error_log('Payment Provider: ' . $paymentProvider);

    // Log items JSON for debugging
    error_log('Items JSON: ' . $items);

    // Try to decode and log individual items
    $decodedItems = json_decode($items, true);
    if ($decodedItems && is_array($decodedItems)) {
        foreach ($decodedItems as $index => $item) {
            error_log("Item {$index}: " . json_encode($item));
        }
    }

    // Return user-friendly error response
    $errorMessage = 'Failed to create order. Please try again later.';

    // For development environment, include more details
    if (defined('ENVIRONMENT') && ENVIRONMENT === 'development') {
        $errorMessage .= ' Error: ' . $e->getMessage();
    }

    // Return error response
    jsonResponse(false, $errorMessage, [], 500);
}
